### **專案更新日誌：從 v0.1 到 v1.4**

本文件記錄了影像播放器從一個基本的 UI 原型演進為一個功能豐富的多鏡頭監控應用程式的過程。

---

### **v0.1: `main_ui.py` - 基礎 UI 原型**

這是專案的第一個圖形化介面版本，旨在將原始 `main.py` 的核心串流功能視覺化。

*   **原始功能**:
    *   使用 `tkinter` 函式庫建立基礎使用者介面。
    *   提供一個 URL 輸入欄位及一個「播放」按鈕。
    *   能夠接收一個網路影像串流 URL 並在畫布上顯示。
*   **主要限制**:
    *   僅支援單一攝影機。
    *   功能非常陽春，沒有錄影或任何進階操作。
    *   影像更新效率不佳，容易產生閃爍。

---

### **v1.1: `videoplayerV1.1_main_ui.py` - 多鏡頭支援、錄影功能與閃爍修復**

此版本是第一次重大重構，引入了核心功能並解決了初版的主要缺陷。

*   **新增功能**:
    *   **多鏡頭架構**：重構程式碼為 `VideoPlayer` 類別，從而實現了並排顯示與獨立控制兩個攝影機畫面的能力。
    *   **手動錄影**：為每個播放器新增了「開始錄影」和「結束錄影」按鈕。
    *   **影片儲存**：錄影內容會以 `.mp4` 格式儲存在程式目錄下的 `captures` 資料夾中。
    *   **OSD (On-Screen Display)**：錄影時，畫面上會疊加紅色的 "REC" 圖示以及即時的錄影秒數計時器。
*   **更新與修復**:
    *   **執行緒問題修復 (根本解決閃爍)**：徹底重構了影像更新邏輯。引入了執行緒安全的 `queue` (佇列) 機制，將背景的影像讀取執行緒與主程式的 UI 更新執行緒完全分離，根除了造成畫面閃爍的競爭條件問題。
    *   **Namespace 錯誤修復**：修正了因錯誤引用 `tkinter.font` 而非 `cv2` 的字體常數所導致的 `AttributeError` 程式崩潰問題。

---

### **v1.2: `videoplayerV1.2_main_ui.py` - 智慧錄影功能**

此版本專注於提升錄影功能的實用性與智慧化。

*   **新增功能**:
    *   **自動分段錄影**：錄影模式從手動開始/結束一個長檔案，改為啟動後每 60 秒自動儲存一個影片片段，並無縫開始下一個片段的錄製。
    *   **增強型 OSD**：畫面上的 OSD 計時器會在每 60 秒分段後自動歸零；同時新增了檔案計數器，讓使用者清楚知道目前正在錄製第幾個片段。
    *   **錄影完整性**：當使用者手動按下「結束錄影」時，即使當前的片段不足 60 秒，也會被立即儲存，確保了操作的即時性與資料的完整性。

---

### **v1.3: `videoplayerV1.3_main_ui.py` - 設定系統與遠端鏡頭操作**

此版本引入了設定保存機制與遠端 PTZ 控制，讓程式從一個簡單工具變為一個可配置的應用。

*   **新增功能**:
    *   **設定保存系統**：新增了 `config.json` 檔案，用於保存各攝影機的名稱和 URL，讓設定在程式關閉後得以保留。
    *   **獨立設定頁面**：主介面新增「Settings」按鈕，點擊後會彈出一個獨立視窗，供使用者方便地修改攝影機設定。
    *   **啟動時自動播放**：程式啟動時會自動讀取 `config.json` 的設定，並立即連線播放影像，省去了手動操作。
    *   **PTZ 鏡頭控制**：
        *   為每個播放器新增了圖形化的方向鍵盤 (D-Pad)，用於遠端控制攝影機的上下左右移動和復位。
        *   程式能根據 `main.py` 中的規則，自動從影像 URL 推導並產生對應的 PTZ 控制 URL。
    *   **新增依賴**：為實現 PTZ 功能，專案中加入了 `requests` 函式庫。

---

### **v1.4: `videoplayerV1.4_main_ui.py` - UI 版面最佳化**

此版本是最後一次針對使用者體驗進行的介面精修。

*   **更新與修復**:
    *   **頂部控制列**：將「播放/暫停」和錄影相關按鈕移至播放器頂部，與攝影機名稱並排，使操作區域更集中。
    *   **懸浮式 PTZ 控制**：使用 `.place()` 佈局管理器，將 PTZ 方向鍵盤從原本的側邊欄改為「懸浮」在影像畫面的右下角，使整體版面更簡潔、現代，並為影像提供了更大的顯示空間。
