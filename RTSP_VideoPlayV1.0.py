
import tkinter as tk
from tkinter import ttk
import vlc
import os
import json
import time

CONFIG_FILE = "config.json"

# --- Configuration Handling ---
def load_config():
    if not os.path.exists(CONFIG_FILE):
        return [{"name": "Camera 1", "url": ""}, {"name": "Camera 2", "url": ""}]
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
            while len(config) < 2:
                config.append({"name": f"Camera {len(config) + 1}", "url": ""})
            return config
    except (json.JSONDecodeError, IOError):
        return [{"name": "Camera 1", "url": ""}, {"name": "Camera 2", "url": ""}]

def save_config(data):
    with open(CONFIG_FILE, 'w') as f:
        json.dump(data, f, indent=4)

# --- Settings Window ---
class SettingsWindow(tk.Toplevel):
    def __init__(self, parent, reload_callback):
        super().__init__(parent)
        self.title("Settings")
        self.transient(parent)
        self.reload_callback = reload_callback
        self.entries = []
        configs = load_config()
        for i, config in enumerate(configs[:2]):
            frame = ttk.LabelFrame(self, text=f"Camera {i+1}", padding="10")
            frame.pack(fill=tk.X, padx=10, pady=5)
            ttk.Label(frame, text="Name:").grid(row=0, column=0, sticky=tk.W, pady=2)
            name_entry = ttk.Entry(frame, width=40)
            name_entry.grid(row=0, column=1, sticky=tk.EW)
            name_entry.insert(0, config.get("name", ""))
            ttk.Label(frame, text="URL (RTSP):").grid(row=1, column=0, sticky=tk.W, pady=2)
            url_entry = ttk.Entry(frame, width=40)
            url_entry.grid(row=1, column=1, sticky=tk.EW)
            url_entry.insert(0, config.get("url", ""))
            self.entries.append({"name": name_entry, "url": url_entry})
        save_button = ttk.Button(self, text="Save and Apply", command=self.save_and_close)
        save_button.pack(pady=10)

    def save_and_close(self):
        new_configs = []
        for entry_pair in self.entries:
            new_configs.append({"name": entry_pair["name"].get(), "url": entry_pair["url"].get()})
        save_config(new_configs)
        self.reload_callback()
        self.destroy()

# --- VLC Player Class ---
class VLCPlayer:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent, padding="5")
        self.frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # --- VLC and State Variables ---
        self.vlc_instance = vlc.Instance()
        self.media_player = self.vlc_instance.media_player_new()
        self.url = ""
        self.is_playing = False

        # --- UI Widgets ---
        top_bar = ttk.Frame(self.frame)
        top_bar.pack(fill=tk.X, pady=(0, 5))

        self.play_button = ttk.Button(top_bar, text="▶ Play", command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 2))
        
        self.stop_button = ttk.Button(top_bar, text="■ Stop", command=self.stop, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=2)

        self.name_label = ttk.Label(self.frame, text="Camera", font=("-weight bold"))
        self.name_label.pack(in_=top_bar, side=tk.LEFT, padx=(10, 0))

        self.video_panel = ttk.Frame(self.frame, relief="sunken", borderwidth=1)
        self.video_panel.pack(fill=tk.BOTH, expand=True)
        self.video_panel.config(cursor="arrow")
        self.video_panel.bind("<Configure>", self._on_resize)

        # Associate VLC with the video panel
        self.media_player.set_hwnd(self.video_panel.winfo_id())

    def _on_resize(self, event):
        # This helps re-associate the window on some platforms/resizes
        self.media_player.set_hwnd(self.video_panel.winfo_id())

    def load_config_and_play(self, config):
        self.stop()
        self.name_label.config(text=config.get("name", "No Name"))
        self.url = config.get("url", "")
        if self.url:
            self.play()

    def toggle_play(self):
        if self.is_playing:
            self.stop()
        elif self.url:
            self.play()

    def play(self):
        if not self.url:
            return
        if self.is_playing:
            self.stop()
            time.sleep(0.1) # Give it a moment to stop before playing again

        media = self.vlc_instance.media_new(self.url)
        self.media_player.set_media(media)
        self.media_player.play()
        self.is_playing = True
        self.play_button.config(text="❚❚ Pause") # Or could be stop, depending on desired logic
        self.stop_button.config(state=tk.NORMAL)

    def stop(self):
        if self.media_player.is_playing():
            self.media_player.stop()
        self.is_playing = False
        self.play_button.config(text="▶ Play")
        self.stop_button.config(state=tk.DISABLED)

    def cleanup(self):
        if self.media_player:
            self.media_player.release()
            self.media_player = None

# --- Main Application ---
if __name__ == "__main__":
    root = tk.Tk()
    root.title("RTSP Video Player V1.0")

    top_frame = ttk.Frame(root)
    top_frame.pack(fill=tk.X, padx=10, pady=5)

    players = []

    def reload_players_with_new_config():
        configs = load_config()
        for i, player in enumerate(players):
            player.load_config_and_play(configs[i])

    def open_settings():
        SettingsWindow(root, reload_players_with_new_config)

    settings_button = ttk.Button(top_frame, text="Settings", command=open_settings)
    settings_button.pack(side=tk.RIGHT)

    player_frame = ttk.Frame(root)
    player_frame.pack(fill=tk.BOTH, expand=True)

    player1 = VLCPlayer(player_frame)
    player2 = VLCPlayer(player_frame)
    players = [player1, player2]

    # Initial load and autoplay
    # We need to give the UI a moment to draw itself before playing
    root.after(100, reload_players_with_new_config)

    def on_closing():
        for player in players:
            player.cleanup()
        # Give vlc time to release resources
        time.sleep(0.5)
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
