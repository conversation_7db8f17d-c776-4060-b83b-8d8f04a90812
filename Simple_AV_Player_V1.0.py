
import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time
import os
import queue
import json
import av # PyAV library
import pyaudio

CONFIG_FILE = "av_config.json"

# --- Configuration Handling ---
def load_config():
    default_config = [{
        "name": "Camera 1", 
        "video_url": "http://user:<EMAIL>/dgvideo.cgi", 
        "audio_url": "http://user:<EMAIL>/dgaudio.cgi"
    }]
    if not os.path.exists(CONFIG_FILE):
        return default_config
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
            if not isinstance(config, list) or not config:
                return default_config
            return config
    except (json.JSONDecodeError, IOError):
        return default_config

def save_config(data):
    with open(CONFIG_FILE, 'w') as f:
        json.dump(data, f, indent=4)

# --- Settings Window (Simplified) ---
class SettingsWindow(tk.Toplevel):
    def __init__(self, parent, reload_callback):
        super().__init__(parent)
        self.title("Settings")
        self.transient(parent)
        self.reload_callback = reload_callback
        self.entries = {}
        configs = load_config()
        config = configs[0]

        frame = ttk.LabelFrame(self, text="Camera Settings", padding="10")
        frame.pack(fill=tk.X, padx=10, pady=5)

        fields = {"Name": "name", "Video URL": "video_url", "Audio URL": "audio_url"}
        for i, (label, key) in enumerate(fields.items()):
            ttk.Label(frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            entry = ttk.Entry(frame, width=50)
            entry.grid(row=i, column=1, sticky=tk.EW)
            entry.insert(0, config.get(key, ""))
            self.entries[key] = entry

        save_button = ttk.Button(self, text="Save and Apply", command=self.save_and_close)
        save_button.pack(pady=10)

    def save_and_close(self):
        new_config = {key: entry.get() for key, entry in self.entries.items()}
        save_config([new_config])
        self.reload_callback()
        self.destroy()

# --- Simple A/V Player Class ---
class SimpleAVPlayer:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent, padding="10")
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.video_thread = None
        self.audio_thread = None
        self.is_playing = False
        self.is_muted = False
        self.frame_queue = queue.Queue(maxsize=10)
        self.config = {}

        top_bar = ttk.Frame(self.frame)
        top_bar.pack(fill=tk.X, pady=(0, 5))
        self.play_button = ttk.Button(top_bar, text="▶ Play", command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0,5))
        self.mute_button = ttk.Button(top_bar, text="🔊", width=3, command=self.toggle_mute, state=tk.DISABLED)
        self.mute_button.pack(side=tk.LEFT)
        self.name_label = ttk.Label(self.frame, text="Camera", font=("-weight bold"))
        self.name_label.pack(in_=top_bar, side=tk.LEFT, padx=(10, 0))
        self.canvas = tk.Canvas(self.frame, width=640, height=480, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        self.image_on_canvas = None

    def load_config_and_play(self, config):
        self.config = config
        self.name_label.config(text=config.get("name", "No Name"))
        if config.get("video_url") or config.get("audio_url"):
            self.start()

    def toggle_play(self):
        if self.is_playing:
            self.stop()
        else:
            self.start()

    def toggle_mute(self):
        self.is_muted = not self.is_muted
        self.mute_button.config(text="🔇" if self.is_muted else "🔊")
        print(f"Audio muted: {self.is_muted}")

    def start(self):
        if self.is_playing:
            return
        if not self.config.get("video_url") and not self.config.get("audio_url"):
            print("No URLs provided.")
            return
        self.is_playing = True
        self.play_button.config(text="■ Stop")
        self.mute_button.config(state=tk.NORMAL)
        if self.config.get("video_url"):
            self.video_thread = threading.Thread(target=self._video_loop, daemon=True)
            self.video_thread.start()
            self._update_canvas()
        if self.config.get("audio_url"):
            self.audio_thread = threading.Thread(target=self._audio_loop_pyav, daemon=True)
            self.audio_thread.start()

    def stop(self):
        self.is_playing = False
        time.sleep(0.2) # Give threads a moment to see the flag
        with self.frame_queue.mutex:
            self.frame_queue.queue.clear()
        self.play_button.config(text="▶ Play")
        self.mute_button.config(state=tk.DISABLED, text="🔊")
        self.is_muted = False
        self.canvas.delete("all")
        self.image_on_canvas = None

    def _video_loop(self):
        cap = cv2.VideoCapture(self.config["video_url"])
        while self.is_playing:
            ret, frame = cap.read()
            if not ret:
                break
            try:
                self.frame_queue.put_nowait(frame)
            except queue.Full:
                pass
        cap.release()
        print("Video thread finished.")

    def _audio_loop_pyav(self):
        p = pyaudio.PyAudio()
        stream = None
        try:
            with av.open(self.config["audio_url"], timeout=5) as container:
                audio_stream = container.streams.audio[0]
                stream = p.open(format=p.get_format_from_width(audio_stream.format.bytes),
                                channels=audio_stream.channels,
                                rate=audio_stream.rate,
                                output=True)
                print(f"PyAV: Audio stream opened. Format: {audio_stream.format.name}, Rate: {audio_stream.rate}, Channels: {audio_stream.channels}")
                for frame in container.decode(audio_stream):
                    if not self.is_playing:
                        break
                    if not self.is_muted:
                        stream.write(frame.to_ndarray().tobytes())
        except av.error.StreamNotFoundError:
            print("PyAV Error: No audio stream found in the URL.")
        except Exception as e:
            print(f"PyAV audio stream failed: {e}")
        finally:
            if stream:
                stream.stop_stream()
                stream.close()
            p.terminate()
            print("Audio thread finished.")

    def _update_canvas(self):
        if not self.is_playing:
            return
        try:
            frame = self.frame_queue.get_nowait()
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            photo = ImageTk.PhotoImage(image=Image.fromarray(frame_rgb))
            if self.image_on_canvas is None:
                self.image_on_canvas = self.canvas.create_image(0, 0, image=photo, anchor=tk.NW)
            else:
                self.canvas.itemconfig(self.image_on_canvas, image=photo)
            self.canvas.image = photo
        except queue.Empty:
            pass
        self.frame.after(20, self._update_canvas)

# --- Main Application ---
if __name__ == "__main__":
    root = tk.Tk()
    root.title("Simple A/V Player V1.1 (PyAV)")
    top_frame = ttk.Frame(root)
    top_frame.pack(fill=tk.X, padx=10, pady=5)
    player = SimpleAVPlayer(root)
    def reload_player_with_new_config():
        configs = load_config()
        player.load_config_and_play(configs[0])
    def open_settings():
        SettingsWindow(root, reload_player_with_new_config)
    settings_button = ttk.Button(top_frame, text="Settings", command=open_settings)
    settings_button.pack(side=tk.RIGHT)
    def on_closing():
        player.stop()
        root.destroy()
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.after(100, reload_player_with_new_config)
    root.mainloop()
