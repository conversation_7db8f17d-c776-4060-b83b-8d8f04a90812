# Video Player V2.1 錄影問題修復報告

## 問題描述

在執行 Video Player V2.1 版本的錄影功能時，出現以下錯誤：

```
A/V recording failed: attribute 'channels' of 'av.audio.codeccontext.AudioCodecContext' objects is not writable
```

## 問題分析

### 根本原因
這個問題是由於 PyAV 版本升級導致的 API 變更：

1. **PyAV 版本**: 當前安裝的是 PyAV 15.0.0
2. **API 變更**: 在 PyAV 15.0.0 中，`AudioCodecContext.channels` 屬性變成了只讀屬性
3. **原始代碼問題**: V2.1 版本中的錄影代碼嘗試直接設置 `out_audio_stream.channels = in_audio_stream.channels`

### 具體錯誤位置
在 `videoplayerV2.1_main_ui.py` 的第 268 行：
```python
out_audio_stream.channels = in_audio_stream.channels  # 這行會導致錯誤
```

## 解決方案

### 主要修復
1. **使用 `add_stream_from_template()` 方法**
   - 替換原來的手動設置編解碼器參數的方式
   - 這個方法會自動複製輸入流的所有參數，包括音頻通道配置

2. **改進的錄影邏輯**
   - 使用更穩定的包處理方式
   - 正確處理視頻和音頻流的同步

### 修復後的代碼
```python
def _recording_loop(self):
    self.recording_start_time = time.time()
    filepath = os.path.join("captures", f"{self.name_label.cget('text')}-{time.strftime('%Y%m%d-%H%M%S')}.mp4")
    try:
        with av.open(self.video_url, timeout=5) as video_in, \
             av.open(self.audio_url, timeout=5) as audio_in, \
             av.open(filepath, mode='w') as output:
            
            in_video_stream = video_in.streams.video[0]
            in_audio_stream = audio_in.streams.audio[0]

            # 使用 add_stream_from_template 確保兼容性
            out_video_stream = output.add_stream_from_template(in_video_stream)
            out_audio_stream = output.add_stream_from_template(in_audio_stream)

            print(f"Started A/V recording to {filepath}")
            print(f"Video: {out_video_stream.codec.name} {out_video_stream.width}x{out_video_stream.height}")
            print(f"Audio: {out_audio_stream.codec.name} {out_audio_stream.rate}Hz")
            
            # 處理來自兩個流的包
            video_done = False
            audio_done = False
            video_packets = video_in.demux(in_video_stream)
            audio_packets = audio_in.demux(in_audio_stream)
            
            while self.is_recording and not (video_done and audio_done):
                # 嘗試獲取視頻包
                if not video_done:
                    try:
                        packet = next(video_packets)
                        if packet.dts is not None:
                            packet.stream = out_video_stream
                            output.mux(packet)
                    except StopIteration:
                        video_done = True
                
                # 嘗試獲取音頻包
                if not audio_done:
                    try:
                        packet = next(audio_packets)
                        if packet.dts is not None:
                            packet.stream = out_audio_stream
                            output.mux(packet)
                    except StopIteration:
                        audio_done = True

    except Exception as e:
        print(f"A/V recording failed: {e}")
    finally:
        print(f"Finished A/V recording to {filepath}")
```

## 修復文件

創建了新的修復版本文件：`videoplayerV2.1_fixed_main_ui.py`

### 主要改進
1. **兼容 PyAV 15.0.0**: 使用新的 API 方法避免只讀屬性問題
2. **更穩定的錄影**: 改進了包處理邏輯
3. **更好的錯誤處理**: 增加了詳細的調試信息

## 測試結果

修復後的版本已經可以正常啟動錄影功能，不再出現 "channels not writable" 錯誤。

### 成功輸出示例
```
Started A/V recording to captures\Camera 1-20250816-213857.mp4
Video: mjpeg 640x480
Audio: pcm_s16le 11025Hz
Finished A/V recording to captures\Camera 1-20250816-213857.mp4
```

## 建議

1. **使用修復版本**: 建議使用 `videoplayerV2.1_fixed_main_ui.py` 替代原來的版本
2. **PyAV 版本管理**: 如果需要保持向後兼容性，可以考慮固定 PyAV 版本
3. **定期更新**: 關注 PyAV 的版本更新和 API 變更

## 技術細節

### PyAV API 變更
- **舊方式**: 手動設置 `channels`, `layout` 等屬性
- **新方式**: 使用 `add_stream_from_template()` 自動複製流參數
- **優勢**: 更安全、更兼容、減少手動配置錯誤

### 兼容性考慮
這個修復方案與 PyAV 15.0.0 完全兼容，同時也應該與較舊版本的 PyAV 兼容。
