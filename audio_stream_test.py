#!/usr/bin/env python3
"""
音頻流測試工具
用於診斷攝影機音頻流的連線狀態和穩定性
"""

import av
import time
import json
import sys
from datetime import datetime

def load_camera_config():
    """載入攝影機配置"""
    try:
        with open('config_v2.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"無法載入配置檔案: {e}")
        return []

def test_audio_stream(name, audio_url, duration=30):
    """測試單一音頻流"""
    print(f"\n=== 測試 {name} 音頻流 ===")
    print(f"URL: {audio_url}")
    print(f"測試時間: {duration} 秒")
    
    start_time = time.time()
    success_count = 0
    error_count = 0
    last_error = None
    
    while time.time() - start_time < duration:
        try:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 嘗試連接...")
            
            with av.open(audio_url, timeout=10) as container:
                if not container.streams.audio:
                    print("  ❌ 沒有找到音頻流")
                    error_count += 1
                    continue
                
                audio_stream = container.streams.audio[0]
                print(f"  ✅ 連接成功")
                print(f"     編解碼器: {audio_stream.codec.name}")
                print(f"     採樣率: {audio_stream.rate}Hz")
                print(f"     聲道數: {audio_stream.channels}")
                print(f"     格式: {audio_stream.format}")
                
                # 嘗試讀取幾個音頻幀
                frame_count = 0
                for frame in container.decode(audio_stream):
                    frame_count += 1
                    if frame_count >= 10:  # 讀取 10 個幀後停止
                        break
                
                print(f"     成功讀取 {frame_count} 個音頻幀")
                success_count += 1
                
        except Exception as e:
            error_count += 1
            last_error = str(e)
            print(f"  ❌ 連接失敗: {e}")
        
        # 等待 5 秒後再次測試
        time.sleep(5)
    
    # 測試結果統計
    total_attempts = success_count + error_count
    success_rate = (success_count / total_attempts * 100) if total_attempts > 0 else 0
    
    print(f"\n--- {name} 測試結果 ---")
    print(f"總嘗試次數: {total_attempts}")
    print(f"成功次數: {success_count}")
    print(f"失敗次數: {error_count}")
    print(f"成功率: {success_rate:.1f}%")
    if last_error:
        print(f"最後錯誤: {last_error}")
    
    return success_rate > 80  # 成功率超過 80% 視為通過

def test_network_connectivity(cameras):
    """測試網路連通性"""
    print("\n=== 網路連通性測試 ===")
    
    import subprocess
    import re
    
    for camera in cameras:
        name = camera.get('name', 'Unknown')
        audio_url = camera.get('audio_url', '')
        
        # 從 URL 中提取 IP 地址
        ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', audio_url)
        if not ip_match:
            print(f"{name}: 無法從 URL 中提取 IP 地址")
            continue
        
        ip = ip_match.group(1)
        print(f"{name} ({ip}): ", end='')
        
        try:
            # Windows ping 命令
            result = subprocess.run(['ping', '-n', '3', ip], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 解析 ping 結果
                output = result.stdout
                if 'Average' in output:
                    avg_match = re.search(r'Average = (\d+)ms', output)
                    if avg_match:
                        avg_time = avg_match.group(1)
                        print(f"✅ 連通 (平均延遲: {avg_time}ms)")
                    else:
                        print("✅ 連通")
                else:
                    print("✅ 連通")
            else:
                print("❌ 無法連通")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")

def main():
    """主程式"""
    print("攝影機音頻流診斷工具")
    print("=" * 50)
    
    # 載入配置
    cameras = load_camera_config()
    if not cameras:
        print("沒有找到攝影機配置")
        return
    
    # 測試網路連通性
    test_network_connectivity(cameras)
    
    # 測試音頻流
    test_duration = 30  # 預設測試 30 秒
    if len(sys.argv) > 1:
        try:
            test_duration = int(sys.argv[1])
        except ValueError:
            print("無效的測試時間，使用預設值 30 秒")
    
    all_passed = True
    
    for camera in cameras:
        name = camera.get('name', 'Unknown')
        audio_url = camera.get('audio_url', '')
        
        if not audio_url:
            print(f"\n{name}: 沒有設定音頻 URL")
            continue
        
        passed = test_audio_stream(name, audio_url, test_duration)
        if not passed:
            all_passed = False
    
    # 總結
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有音頻流測試通過")
    else:
        print("❌ 部分音頻流測試失敗")
        print("\n建議檢查項目:")
        print("1. 網路連線穩定性")
        print("2. 攝影機設定")
        print("3. 防火牆設定")
        print("4. 攝影機負載狀況")

if __name__ == "__main__":
    main()
