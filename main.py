from flask import Flask, render_template, Response, request, redirect, url_for
import cv2



app = Flask(__name__)
#cap = cv2.VideoCapture(0)

URLS = [
	"********************************/dgvideo.cgi", #A1攝影機
	"********************************/dgvideo.cgi", #A2攝影機
	"********************************/dgvideo.cgi", #A3攝影機
	"********************************/dgvideo.cgi", #B1攝影機
	"********************************/dgvideo.cgi", #B2攝影機
	"********************************/dgvideo.cgi", #B3攝影機
	"********************************/dgvideo.cgi", #B4攝影機
	"********************************/dgvideo.cgi", #B5攝影機
	"********************************/dgvideo.cgi", #B6攝影機
	"********************************/dgvideo.cgi", #C1攝影機
	"********************************/dgvideo.cgi", #C2攝影機
	"********************************/dgvideo.cgi", #C3攝影機
	"********************************/dgvideo.cgi", #C4攝影機
	"********************************/dgvideo.cgi", #D1攝影機
	"********************************/dgvideo.cgi", #D2攝影機
	"********************************/dgvideo.cgi", #E1攝影機
	"********************************/dgvideo.cgi", #E2攝影機
	"********************************/dgvideo.cgi", #E3攝影機
	"********************************/dgvideo.cgi" #E4攝影機
]

MOV_URLS = [
	"*********************************/pantiltcontrol.cgi", #A1攝影機
	"*********************************/pantiltcontrol.cgi", #A2攝影機
	"*********************************/pantiltcontrol.cgi", #A3攝影機
	"*********************************/pantiltcontrol.cgi", #B1攝影機
	"*********************************/pantiltcontrol.cgi", #B2攝影機
	"*********************************/pantiltcontrol.cgi", #B3攝影機
	"*********************************/pantiltcontrol.cgi", #B4攝影機
	"*********************************/pantiltcontrol.cgi", #B5攝影機
	"*********************************/pantiltcontrol.cgi", #B6攝影機
	"*********************************/pantiltcontrol.cgi", #C1攝影機
	"*********************************/pantiltcontrol.cgi", #C2攝影機
	"*********************************/pantiltcontrol.cgi", #C3攝影機
	"*********************************/pantiltcontrol.cgi", #C4攝影機
	"*********************************/pantiltcontrol.cgi", #D1攝影機
	"*********************************/pantiltcontrol.cgi", #D2攝影機
	"*********************************/pantiltcontrol.cgi", #E1攝影機
	"*********************************/pantiltcontrol.cgi", #E2攝影機
	"*********************************/pantiltcontrol.cgi", #E3攝影機
	"*********************************/pantiltcontrol.cgi" #E4攝影機
]


###########################################################################################

###########################################################################################

import requests



@app.route("/move_up/<int:camera_index>/<string:i_move_type>")
def move_up(camera_index,i_move_type):
	#url = '*********************************/pantiltcontrol.cgi'
	
	#move_type=i_move_type
	url = MOV_URLS[camera_index]
	myobj = {'PanSingleMoveDegree': '5','TiltSingleMoveDegree': '5','PanTiltSingleMove':i_move_type}
	x = requests.post(url, data = myobj)
	
	if i_move_type=="1":
		return  "第"+str(camera_index+1)+ "台攝影機，向上移動!";
	elif i_move_type=="7":
		return  "第"+str(camera_index+1)+ "台攝影機，向下移動!";
	elif i_move_type=="3":
		return  "第"+str(camera_index+1)+ "台攝影機，向左移動!";
	elif i_move_type=="5":
		return  "第"+str(camera_index+1)+ "台攝影機，向右移動!";
	elif i_move_type=="4":
		return  "第"+str(camera_index+1)+ "台攝影機，復位!";
	else:
		return  "第"+str(camera_index+1)+ "台攝影機，不移動!";
###########################################################################################

############################################################################################
selectV=0

#單一支
def get_frames():
	#"********************************/dgh264.raw"
	URL = URLS[selectV]
	cap = cv2.VideoCapture(URL)
	while True:
		ret, frame = cap.read()
		if not ret:
			break
		else:
			_, buffer = cv2.imencode(".jpg", frame)
			frame = buffer.tobytes()
			yield (b"--frame\r\n"
				b"Content-Type: image/jpeg\r\n\r\n" + frame + b"\r\n")	


@app.route("/A1")
def video_feed_A1():
	global selectV		# 聲明下方的 a 為全域變數 a
	selectV=0 # 修改 a 為 2
	return render_template("video.html")
	
@app.route("/A2")
def video_feed_A2():
	global selectV		# 聲明下方的 a 為全域變數 a
	selectV=1
	return render_template("video.html")


#單一支
@app.route("/video_feed")
def video_feed():
	return Response(get_frames(),
		mimetype="multipart/x-mixed-replace; boundary=frame")
		

	
	
############################################################################################	

# 修改路由，加入攝影機索引
def get_multi_frames(urls):
	caps = [cv2.VideoCapture(url) for url in urls]

	while True:
		frames = []
		for cap in caps:
			ret, frame = cap.read()
			if not ret:
				break
			_, buffer = cv2.imencode(".jpg", frame)
			frames.append(buffer.tobytes())

		if len(frames) != len(urls):
			break

		frame_data = b""
		for frame in frames:
			frame_data += (b"--frame\r\n"
							b"Content-Type: image/jpeg\r\n\r\n" + frame + b"\r\n")
		yield frame_data


# 修改路由，加入攝影機索引
@app.route("/video_feed/<int:camera_index>")	# 修改路由，加入攝影機索引
def video_multi_feed(camera_index):
	if camera_index < len(URLS):
		return Response(get_multi_frames([URLS[camera_index]]), mimetype="multipart/x-mixed-replace; boundary=frame")
	else:
		return "Invalid camera index."


############################################################################################


@app.route("/hello")
def hello():
	return "Hello World1!"

@app.route("/multi")
def mulit_video():
	camera_urls = range(len(URLS))	# 修改為攝影機索引的範圍
	return render_template("video2to2refresh.html", camera_urls=camera_urls)
	
@app.route("/")
def index():
	return render_template("video.html")

if __name__ == "__main__":
	app.run(host="0.0.0.0",port="3388",debug=False,ssl_context='adhoc')
	
	