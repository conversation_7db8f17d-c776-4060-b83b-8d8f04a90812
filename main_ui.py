
import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time
import os

class VideoPlayer:
    def __init__(self, root):
        self.root = root
        self.root.title("Video Player")

        # Create a directory for captures if it doesn't exist
        if not os.path.exists("captures"):
            os.makedirs("captures")

        self.url_label = ttk.Label(root, text="Enter URL:")
        self.url_label.pack(pady=5)

        self.url_entry = ttk.Entry(root, width=50)
        self.url_entry.pack(pady=5)

        self.play_button = ttk.Button(root, text="Play", command=self.play_video)
        self.play_button.pack(pady=5)

        self.canvas = tk.Canvas(root, width=640, height=480)
        self.canvas.pack()

        self.cap = None
        self.is_playing = False
        self.thread = None

    def play_video(self):
        if self.is_playing:
            self.stop_video()

        url = self.url_entry.get()
        if url:
            self.is_playing = True
            self.thread = threading.Thread(target=self.stream, args=(url,))
            self.thread.daemon = True
            self.thread.start()

    def stream(self, url):
        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "verify_peer;0"
        self.cap = cv2.VideoCapture(url)
        
        video_writer = None
        recording_start_time = 0
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        if fps == 0:
            fps = 20.0  # Default FPS

        while self.is_playing:
            ret, frame = self.cap.read()
            if not ret:
                break

            # Video recording logic
            if video_writer is None:
                height, width, _ = frame.shape
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                timestamp = time.strftime("%Y%m%d-%H%M%S")
                filepath = os.path.join("captures", f"recording-{timestamp}.mp4")
                video_writer = cv2.VideoWriter(filepath, fourcc, fps, (width, height))
                recording_start_time = time.time()
                print(f"Started recording to {filepath}")

            video_writer.write(frame)

            if time.time() - recording_start_time >= 60:
                video_writer.release()
                print(f"Finished recording.")
                video_writer = None

            # Convert frame for display
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            self.photo = ImageTk.PhotoImage(image=Image.fromarray(frame_rgb))
            self.canvas.create_image(0, 0, image=self.photo, anchor=tk.NW)

        if video_writer is not None:
            video_writer.release()
            print(f"Finished final recording.")

        self.cap.release()

    def stop_video(self):
        self.is_playing = False
        if self.thread and self.thread.is_alive():
            self.thread.join()

    def on_closing(self):
        self.stop_video()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    player = VideoPlayer(root)
    root.protocol("WM_DELETE_WINDOW", player.on_closing)
    root.mainloop()
