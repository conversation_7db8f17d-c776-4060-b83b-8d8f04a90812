Metadata-Version: 2.1
Name: python-vlc
Version: 3.0.21203
Summary: VLC bindings for python.
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License: LGPL-2.1+
Project-URL: Homepage, https://wiki.videolan.org/PythonBinding
Project-URL: Documentation, https://www.olivieraubert.net/vlc/python-ctypes/doc/
Project-URL: Repository, https://github.com/oaubert/python-vlc.git
Project-URL: Alternate repository, https://git.videolan.org/?p=vlc/bindings/python.git;a=summary
Project-URL: Bug Tracker, https://github.com/oaubert/python-vlc/issues
Keywords: vlc,video
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: POSIX :: Other
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Multimedia
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Description-Content-Type: text/x-rst
License-File: COPYING

Python ctypes-based bindings for libvlc
=======================================

The bindings use ctypes to directly call the libvlc dynamic lib, and
the code is generated from the include files defining the public
API. The same module should be compatible with various versions of
libvlc 3.*. However, there may be incompatible changes between major
versions.

Installing the module
---------------------

You can install the module through PyPI:

    pip install python-vlc

Using the module
----------------

The module offers two ways of accessing the API - a raw access to all
exported methods, and more convenient wrapper classes.

Using wrapper classes
+++++++++++++++++++++

Most major structures of the libvlc API (Instance, Media, MediaPlayer,
etc) are wrapped as classes, with shorter method names and some
adaptations to provide a more pythonic API:

    >>> import vlc
    >>> player = vlc.MediaPlayer('file:///tmp/foo.avi')
    >>> player.play()
    >>> player.get_instance() # returns the corresponding instance

In this case, a default ``vlc.Instance`` will be instanciated and
stored in ``vlc._default_instance``. It will be used to instanciate
the various classes (``Media``, ``MediaList``, ``MediaPlayer``, etc).

You also can use wrapper methods closer to the original libvlc API:

    >>> import vlc
    >>> instance = vlc.Instance('--no-audio', '--fullscreen')
    >>> player = instance.media_player_new()
    >>> player.audio_get_volume()
    50
    >>> media = instance.media_new('file:///tmp/foo.avi')
    >>> media.get_mrl()
    'file:///tmp/foo.avi'
    >>> player.set_media(m)
    >>> player.play()

Using raw access
++++++++++++++++

Libvlc methods are available as attributes of the vlc module (as
  vlc.libvlc_*). Use their docstring (any introspective shell like
  ipython is your friend) to explore them, or refer to the online
  documentation at https://olivieraubert.net/vlc/python-ctypes/

    >>> import vlc
    >>> vlc.libvlc_get_version()
    '3.0.0-rc2 Vetinari'
    >>> exc = vlc.VLCException()
    >>> instance = vlc.libvlc_new(0, [], exc)
    >>> instance
    <vlc.Instance object at 0x8384a4c>
    >>> vlc.libvlc_audio_get_volume(instance, exc)
    50

Example code
++++++++++++

You can find [example
files](https://github.com/oaubert/python-vlc/tree/master/examples) in
the repository.

Note that the ``vlc.py`` module can itself be invoked as an
application using its own features, which also serves as a API usage
example. See the [end of the
module](https://github.com/oaubert/python-vlc/blob/master/generated/3.0/vlc.py#L12525)
after the line ``if __name__ == "__main__":``

License
-------

The generated module is licensed, like libvlc, under the GNU Lesser
General Public License 2.1 or later.
