
import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time
import os
import queue
import json
import requests

CONFIG_FILE = "config.json"

# --- Configuration Handling ---
def load_config():
    if not os.path.exists(CONFIG_FILE):
        return [{"name": "Camera 1", "url": ""}, {"name": "Camera 2", "url": ""}]
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
            while len(config) < 2:
                config.append({"name": f"Camera {len(config) + 1}", "url": ""})
            return config
    except (json.JSONDecodeError, IOError):
        return [{"name": "Camera 1", "url": ""}, {"name": "Camera 2", "url": ""}]

def save_config(data):
    with open(CONFIG_FILE, 'w') as f:
        json.dump(data, f, indent=4)

# --- Settings Window ---
class SettingsWindow(tk.Toplevel):
    def __init__(self, parent, reload_callback):
        super().__init__(parent)
        self.title("Settings")
        self.transient(parent)
        self.reload_callback = reload_callback
        self.entries = []
        configs = load_config()
        for i, config in enumerate(configs[:2]):
            frame = ttk.LabelFrame(self, text=f"Camera {i+1}", padding="10")
            frame.pack(fill=tk.X, padx=10, pady=5)
            ttk.Label(frame, text="Name:").grid(row=0, column=0, sticky=tk.W, pady=2)
            name_entry = ttk.Entry(frame, width=40)
            name_entry.grid(row=0, column=1, sticky=tk.EW)
            name_entry.insert(0, config.get("name", ""))
            ttk.Label(frame, text="URL:").grid(row=1, column=0, sticky=tk.W, pady=2)
            url_entry = ttk.Entry(frame, width=40)
            url_entry.grid(row=1, column=1, sticky=tk.EW)
            url_entry.insert(0, config.get("url", ""))
            self.entries.append({"name": name_entry, "url": url_entry})
        save_button = ttk.Button(self, text="Save and Apply", command=self.save_and_close)
        save_button.pack(pady=10)

    def save_and_close(self):
        new_configs = []
        for entry_pair in self.entries:
            new_configs.append({"name": entry_pair["name"].get(), "url": entry_pair["url"].get()})
        save_config(new_configs)
        self.reload_callback()
        self.destroy()

# --- Video Player Class ---
class VideoPlayer:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent, padding="5")
        self.frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        # ... (Variable initializations are the same)
        self.cap = None
        self.thread = None
        self.video_writer = None
        self.image_on_canvas = None
        self.frame_queue = queue.Queue(maxsize=10)
        self.is_playing = False
        self.is_recording = False
        self.recording_start_time = 0
        self.saved_file_count = 0
        self.url = ""

        # --- UI Layout ---
        # Top bar for controls and name
        top_bar = ttk.Frame(self.frame)
        top_bar.pack(fill=tk.X, pady=(0, 5))

        self.play_button = ttk.Button(top_bar, text="▶", width=3, command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 2))
        self.start_rec_button = ttk.Button(top_bar, text="● Rec", command=self.start_recording, state=tk.DISABLED)
        self.start_rec_button.pack(side=tk.LEFT, padx=2)
        self.stop_rec_button = ttk.Button(top_bar, text="■ Stop", command=self.stop_recording, state=tk.DISABLED)
        self.stop_rec_button.pack(side=tk.LEFT, padx=2)
        self.name_label = ttk.Label(self.frame, text="Camera", font=("-weight bold"))
        self.name_label.pack(in_=top_bar, side=tk.LEFT, padx=(10, 0))

        # Canvas and floating PTZ controls container
        canvas_container = ttk.Frame(self.frame)
        canvas_container.pack(fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(canvas_container, width=640, height=480, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Floating PTZ Controls
        ptz_frame = ttk.Frame(canvas_container)
        ptz_frame.place(relx=1.0, rely=1.0, anchor='se', x=-10, y=-10)
        self.ptz_buttons = {}
        ptz_layout = [('▲', '1', 0, 1), ('◀', '3', 1, 0), ('H', '4', 1, 1), ('▶', '5', 1, 2), ('▼', '7', 2, 1)]
        for text, move_type, r, c in ptz_layout:
            btn = ttk.Button(ptz_frame, text=text, width=3, command=lambda mt=move_type: self.send_ptz_command(mt))
            btn.grid(row=r, column=c)
            self.ptz_buttons[move_type] = btn
        self.set_ptz_state(tk.DISABLED)

    def set_ptz_state(self, state):
        for btn in self.ptz_buttons.values():
            btn.config(state=state)

    def send_ptz_command(self, move_type):
        if not self.url:
            return
        threading.Thread(target=self._execute_ptz_request, args=(move_type,)).start()

    def _execute_ptz_request(self, move_type):
        try:
            control_url = self.url.replace("user:", "admin:", 1).replace("/dgvideo.cgi", "/pantiltcontrol.cgi", 1)
            if control_url == self.url:
                print("PTZ Error: URL format not recognized for PTZ control.")
                return
            payload = {'PanSingleMoveDegree': '5', 'TiltSingleMoveDegree': '5', 'PanTiltSingleMove': move_type}
            r = requests.post(control_url, data=payload, timeout=3)
            if r.status_code == 200:
                print(f"PTZ command '{move_type}' sent successfully.")
            else:
                print(f"PTZ command failed with status: {r.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"PTZ request failed: {e}")
        except Exception as e:
            print(f"An unexpected error occurred during PTZ request: {e}")

    def load_config_and_play(self, config):
        if self.is_playing:
            self.stop_video_stream()
        self.name_label.config(text=config.get("name", "No Name"))
        self.url = config.get("url", "")
        if self.url:
            self.start_video_stream()

    def toggle_play(self):
        if self.is_playing:
            self.stop_video_stream()
        elif self.url:
            self.start_video_stream()

    def start_video_stream(self):
        self.is_playing = True
        self.play_button.config(text="❚❚")
        self.thread = threading.Thread(target=self.stream_loop, args=(self.url,))
        self.thread.daemon = True
        self.thread.start()
        self.update_canvas()

    def stop_video_stream(self):
        self.is_playing = False
        with self.frame_queue.mutex:
            self.frame_queue.queue.clear()
        self.play_button.config(text="▶")
        self.start_rec_button.config(state=tk.DISABLED)
        self.set_ptz_state(tk.DISABLED)

    def update_canvas(self):
        if not self.is_playing:
            return
        try:
            frame = self.frame_queue.get_nowait()
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            self.photo = ImageTk.PhotoImage(image=Image.fromarray(frame_rgb))
            if self.image_on_canvas is None:
                self.image_on_canvas = self.canvas.create_image(0, 0, image=self.photo, anchor=tk.NW)
            else:
                self.canvas.itemconfig(self.image_on_canvas, image=self.photo)
        except queue.Empty:
            pass
        self.frame.after(20, self.update_canvas)

    def start_recording(self):
        if not self.is_playing or self.is_recording:
            return
        self.is_recording = True
        self.saved_file_count = 0
        self.start_rec_button.config(state=tk.DISABLED)
        self.stop_rec_button.config(state=tk.NORMAL)

    def stop_recording(self):
        self.is_recording = False
        self.start_rec_button.config(state=tk.NORMAL if self.is_playing else tk.DISABLED)
        self.stop_rec_button.config(state=tk.DISABLED)

    def stream_loop(self, url):
        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "verify_peer;0"
        self.cap = cv2.VideoCapture(url)
        if not self.cap.isOpened():
            print(f"Error: Cannot open URL: {url}")
            self.is_playing = False
            return
        self.parent.after(0, lambda: self.start_rec_button.config(state=tk.NORMAL))
        self.parent.after(0, lambda: self.set_ptz_state(tk.NORMAL))
        while self.is_playing:
            ret, frame = self.cap.read()
            if not ret:
                break
            if self.is_recording:
                if self.video_writer is None:
                    self.recording_start_time = time.time()
                    height, width, _ = frame.shape
                    fps = self.cap.get(cv2.CAP_PROP_FPS) or 20.0
                    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                    timestamp = time.strftime("%Y%m%d-%H%M%S")
                    filepath = os.path.join("captures", f"recording-{timestamp}-part{self.saved_file_count + 1}.mp4")
                    self.video_writer = cv2.VideoWriter(filepath, fourcc, fps, (width, height))
                    print(f"Started recording to {filepath}")
                elapsed_seconds = int(time.time() - self.recording_start_time)
                if elapsed_seconds >= 60:
                    self.video_writer.release()
                    print(f"Finished segment {self.saved_file_count + 1}.")
                    self.saved_file_count += 1
                    self.video_writer = None
                    continue
                rec_text = f"REC {elapsed_seconds}s | Files: {self.saved_file_count}"
                cv2.circle(frame, (30, 30), 10, (0, 0, 255), -1)
                cv2.putText(frame, rec_text, (50, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                self.video_writer.write(frame)
            try:
                self.frame_queue.put_nowait(frame)
            except queue.Full:
                pass
        if self.video_writer:
            self.video_writer.release()
            print("Saved final recording segment.")
            self.video_writer = None
        if self.cap:
            self.cap.release()
            self.cap = None
        self.parent.after(50, self.clear_canvas)

    def clear_canvas(self):
        self.canvas.delete("all")
        self.image_on_canvas = None
        self.play_button.config(text="▶")
        self.start_rec_button.config(state=tk.DISABLED)
        self.stop_rec_button.config(state=tk.DISABLED)
        self.set_ptz_state(tk.DISABLED)

    def cleanup(self):
        self.is_playing = False

# --- Main Application ---
if __name__ == "__main__":
    root = tk.Tk()
    root.title("Video Player V1.4")
    if not os.path.exists("captures"):
        os.makedirs("captures")
    top_frame = ttk.Frame(root)
    top_frame.pack(fill=tk.X, padx=10, pady=5)
    players = []
    def reload_players_with_new_config():
        configs = load_config()
        for i, player in enumerate(players):
            player.load_config_and_play(configs[i])
    def open_settings():
        SettingsWindow(root, reload_players_with_new_config)
    settings_button = ttk.Button(top_frame, text="Settings", command=open_settings)
    settings_button.pack(side=tk.RIGHT)
    player_frame = ttk.Frame(root)
    player_frame.pack(fill=tk.BOTH, expand=True)
    player1 = VideoPlayer(player_frame)
    player2 = VideoPlayer(player_frame)
    players = [player1, player2]
    reload_players_with_new_config()
    def on_closing():
        for player in players:
            player.cleanup()
        time.sleep(0.2)
        root.destroy()
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
