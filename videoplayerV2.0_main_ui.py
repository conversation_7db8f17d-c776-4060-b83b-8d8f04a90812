
import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time
import os
import queue
import json
import requests
import av
import pyaudio

CONFIG_FILE = "config_v2.json"

# --- Configuration Handling ---
def load_config():
    default_config = [
        {"name": "Camera 1", "video_url": "", "audio_url": ""},
        {"name": "Camera 2", "video_url": "", "audio_url": ""}
    ]
    if not os.path.exists(CONFIG_FILE):
        return default_config
    try:
        with open(CONFIG_FILE, 'r') as f:
            configs = json.load(f)
            # Ensure config has at least 2 entries and all keys
            while len(configs) < 2:
                configs.append({"name": f"Camera {len(configs) + 1}", "video_url": "", "audio_url": ""})
            for cfg in configs:
                cfg.setdefault("name", "Camera")
                cfg.setdefault("video_url", "")
                cfg.setdefault("audio_url", "")
            return configs
    except (json.JSONDecodeError, IOError):
        return default_config

def save_config(data):
    with open(CONFIG_FILE, 'w') as f:
        json.dump(data, f, indent=4)

# --- Settings Window ---
class SettingsWindow(tk.Toplevel):
    def __init__(self, parent, reload_callback):
        super().__init__(parent)
        self.title("Settings")
        self.transient(parent)
        self.reload_callback = reload_callback
        self.entries = []
        configs = load_config()
        for i, config in enumerate(configs[:2]):
            frame = ttk.LabelFrame(self, text=f"Camera {i+1}", padding="10")
            frame.pack(fill=tk.X, padx=10, pady=5)
            
            entry_set = {}
            fields = {"Name": "name", "Video URL": "video_url", "Audio URL": "audio_url"}
            for row, (label, key) in enumerate(fields.items()):
                ttk.Label(frame, text=f"{label}:").grid(row=row, column=0, sticky=tk.W, pady=2)
                entry = ttk.Entry(frame, width=50)
                entry.grid(row=row, column=1, sticky=tk.EW)
                entry.insert(0, config.get(key, ""))
                entry_set[key] = entry
            self.entries.append(entry_set)

        save_button = ttk.Button(self, text="Save and Apply", command=self.save_and_close)
        save_button.pack(pady=10)

    def save_and_close(self):
        new_configs = []
        for entry_set in self.entries:
            new_configs.append({key: entry.get() for key, entry in entry_set.items()})
        save_config(new_configs)
        self.reload_callback()
        self.destroy()

# --- Video Player Class (V2.0) ---
class VideoPlayer:
    def __init__(self, parent):
        # ... UI and Variable setup ...
        self.parent = parent
        self.frame = ttk.Frame(parent, padding="5")
        self.frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.video_thread, self.audio_thread = None, None
        self.video_writer, self.image_on_canvas = None, None
        self.cap = None
        self.frame_queue = queue.Queue(maxsize=10)
        self.is_playing, self.is_recording, self.is_muted = False, False, True
        self.recording_start_time, self.saved_file_count = 0, 0
        self.video_url, self.audio_url = "", ""

        # --- UI Layout ---
        top_bar = ttk.Frame(self.frame)
        top_bar.pack(fill=tk.X, pady=(0, 5))
        self.play_button = ttk.Button(top_bar, text="▶", width=3, command=self.toggle_stream)
        self.play_button.pack(side=tk.LEFT, padx=(0, 2))
        self.mute_button = ttk.Button(top_bar, text="🔇", width=3, command=self.toggle_mute, state=tk.DISABLED)
        self.mute_button.pack(side=tk.LEFT, padx=2)
        self.start_rec_button = ttk.Button(top_bar, text="● Rec", command=self.start_recording, state=tk.DISABLED)
        self.start_rec_button.pack(side=tk.LEFT, padx=2)
        self.stop_rec_button = ttk.Button(top_bar, text="■ Stop", command=self.stop_recording, state=tk.DISABLED)
        self.stop_rec_button.pack(side=tk.LEFT, padx=2)
        self.name_label = ttk.Label(self.frame, text="Camera", font=("-weight bold"))
        self.name_label.pack(in_=top_bar, side=tk.LEFT, padx=(10, 0))

        canvas_container = ttk.Frame(self.frame)
        canvas_container.pack(fill=tk.BOTH, expand=True)
        self.canvas = tk.Canvas(canvas_container, width=640, height=480, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        ptz_frame = ttk.Frame(canvas_container)
        ptz_frame.place(relx=1.0, rely=1.0, anchor='se', x=-10, y=-10)
        self.ptz_buttons = {}
        ptz_layout = [('▲', '1', 0, 1), ('◀', '3', 1, 0), ('H', '4', 1, 1), ('▶', '5', 1, 2), ('▼', '7', 2, 1)]
        for text, move_type, r, c in ptz_layout:
            btn = ttk.Button(ptz_frame, text=text, width=3, command=lambda mt=move_type: self.send_ptz_command(mt))
            btn.grid(row=r, column=c)
            self.ptz_buttons[move_type] = btn
        self.set_ptz_state(tk.DISABLED)

    def set_ptz_state(self, state):
        for btn in self.ptz_buttons.values():
            btn.config(state=state)

    def send_ptz_command(self, move_type):
        if not self.video_url:
            return
        threading.Thread(target=self._execute_ptz_request, args=(move_type,)).start()

    def _execute_ptz_request(self, move_type):
        try:
            control_url = self.video_url.replace("user:", "admin:", 1).replace("/dgvideo.cgi", "/pantiltcontrol.cgi", 1)
            if control_url == self.video_url:
                print("PTZ Error: URL format not recognized.")
                return
            payload = {'PanSingleMoveDegree': '5', 'TiltSingleMoveDegree': '5', 'PanTiltSingleMove': move_type}
            requests.post(control_url, data=payload, timeout=3)
        except Exception as e:
            print(f"PTZ request failed: {e}")

    def load_config_and_play(self, config):
        if self.is_playing:
            self.stop_stream()
        self.name_label.config(text=config.get("name", "No Name"))
        self.video_url = config.get("video_url", "")
        self.audio_url = config.get("audio_url", "")
        if self.video_url:
            self.start_stream()

    def toggle_stream(self):
        if self.is_playing:
            self.stop_stream()
        elif self.video_url:
            self.start_stream()

    def toggle_mute(self):
        self.is_muted = not self.is_muted
        self.mute_button.config(text="🔇" if self.is_muted else "🔊")

    def start_stream(self):
        if self.is_playing:
            return
        self.is_playing = True
        self.play_button.config(text="❚❚")
        self.parent.after(0, lambda: self.start_rec_button.config(state=tk.NORMAL))
        self.parent.after(0, lambda: self.set_ptz_state(tk.NORMAL))
        self.parent.after(0, lambda: self.mute_button.config(state=tk.NORMAL))

        self.video_thread = threading.Thread(target=self._video_loop, daemon=True)
        self.video_thread.start()
        self._update_canvas()
        if self.audio_url:
            self.audio_thread = threading.Thread(target=self._audio_loop, daemon=True)
            self.audio_thread.start()

    def stop_stream(self):
        self.is_playing = False
        time.sleep(0.2)
        with self.frame_queue.mutex:
            self.frame_queue.queue.clear()
        self.play_button.config(text="▶")
        self.start_rec_button.config(state=tk.DISABLED)
        self.stop_rec_button.config(state=tk.DISABLED)
        self.set_ptz_state(tk.DISABLED)
        self.mute_button.config(state=tk.DISABLED, text="🔇")
        self.is_muted = True
        self.parent.after(50, self.clear_canvas)

    def _video_loop(self):
        self.cap = cv2.VideoCapture(self.video_url)
        while self.is_playing:
            ret, frame = self.cap.read()
            if not ret:
                break
            if self.is_recording:
                self._handle_recording(frame)
            try:
                self.frame_queue.put_nowait(frame)
            except queue.Full:
                pass
        if self.cap:
            self.cap.release()
            self.cap = None
        if self.is_recording:
            self.stop_recording()

    def _audio_loop(self):
        p = pyaudio.PyAudio()
        stream = None
        try:
            with av.open(self.audio_url, timeout=5) as container:
                audio_stream = container.streams.audio[0]
                stream = p.open(format=p.get_format_from_width(audio_stream.format.bytes),
                                channels=audio_stream.channels, rate=audio_stream.rate, output=True)
                for frame in container.decode(audio_stream):
                    if not self.is_playing:
                        break
                    if not self.is_muted:
                        stream.write(frame.to_ndarray().tobytes())
        except Exception as e:
            print(f"Audio stream failed for {self.name_label.cget('text')}: {e}")
        finally:
            if stream:
                stream.stop_stream()
                stream.close()
            p.terminate()

    def _update_canvas(self):
        if not self.is_playing:
            return
        try:
            frame = self.frame_queue.get_nowait()
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            self.photo = ImageTk.PhotoImage(image=Image.fromarray(frame_rgb))
            if self.image_on_canvas is None:
                self.image_on_canvas = self.canvas.create_image(0, 0, image=self.photo, anchor=tk.NW)
            else:
                self.canvas.itemconfig(self.image_on_canvas, image=self.photo)
        except queue.Empty:
            pass
        self.frame.after(20, self._update_canvas)

    def start_recording(self):
        if not self.is_playing or self.is_recording:
            return
        self.is_recording = True
        self.saved_file_count = 0
        self.start_rec_button.config(state=tk.DISABLED)
        self.stop_rec_button.config(state=tk.NORMAL)

    def stop_recording(self):
        self.is_recording = False
        self.start_rec_button.config(state=tk.NORMAL if self.is_playing else tk.DISABLED)
        self.stop_rec_button.config(state=tk.DISABLED)

    def _handle_recording(self, frame):
        if self.video_writer is None:
            self.recording_start_time = time.time()
            height, width, _ = frame.shape
            fps = self.cap.get(cv2.CAP_PROP_FPS) or 20.0
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            filepath = os.path.join("captures", f"{self.name_label.cget('text')}-{timestamp}-part{self.saved_file_count + 1}.mp4")
            self.video_writer = cv2.VideoWriter(filepath, fourcc, fps, (width, height))
            print(f"Started recording to {filepath}")
        elapsed_seconds = int(time.time() - self.recording_start_time)
        if elapsed_seconds >= 60:
            self.video_writer.release()
            print(f"Finished segment {self.saved_file_count + 1}.")
            self.saved_file_count += 1
            self.video_writer = None
            return
        rec_text = f"REC {elapsed_seconds}s | Files: {self.saved_file_count}"
        cv2.circle(frame, (30, 30), 10, (0, 0, 255), -1)
        cv2.putText(frame, rec_text, (50, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        self.video_writer.write(frame)

    def clear_canvas(self):
        self.canvas.delete("all")
        self.image_on_canvas = None

    def cleanup(self):
        self.is_playing = False

# --- Main Application ---
if __name__ == "__main__":
    root = tk.Tk()
    root.title("Video Player V2.0")
    if not os.path.exists("captures"):
        os.makedirs("captures")
    top_frame = ttk.Frame(root)
    top_frame.pack(fill=tk.X, padx=10, pady=5)
    players = []
    def reload_players_with_new_config():
        configs = load_config()
        if not players:
            return
        # Stagger the start of the players to avoid audio device conflicts
        players[0].load_config_and_play(configs[0])
        if len(players) > 1:
            root.after(1000, lambda: players[1].load_config_and_play(configs[1]))

    def open_settings():
        SettingsWindow(root, reload_players_with_new_config)
    settings_button = ttk.Button(top_frame, text="Settings", command=open_settings)
    settings_button.pack(side=tk.RIGHT)
    player_frame = ttk.Frame(root)
    player_frame.pack(fill=tk.BOTH, expand=True)
    player1 = VideoPlayer(player_frame)
    player2 = VideoPlayer(player_frame)
    players = [player1, player2]
    reload_players_with_new_config()
    def on_closing():
        for player in players:
            player.cleanup()
        time.sleep(0.5)
        root.destroy()
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
