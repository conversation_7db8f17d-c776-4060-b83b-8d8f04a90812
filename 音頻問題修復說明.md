# 音頻流錯誤問題修復說明

## 問題描述

在錄影過程中出現以下錯誤：
```
Audio stream failed for Camera 1: [Errno 1414092869] Immediate exit requested: '*****************************************/dgaudio.cgi'
Audio stream failed for Camera 2: [Errno 1414092869] Immediate exit requested: '***********************************/dgaudio.cgi'
```

## 問題分析

### 錯誤代碼含義
- `[Errno 1414092869]` 對應 FFmpeg 的 `AVERROR_EXIT` 錯誤
- `Immediate exit requested` 表示音頻流被強制中斷

### 可能原因
1. **網路連線不穩定**: 攝影機網路連線間歇性中斷
2. **資源競爭**: 同時播放和錄影造成音頻流衝突
3. **攝影機負載**: 兩台攝影機同時存取可能造成負載過重
4. **超時設定**: 原始 5 秒超時可能過短
5. **音頻編解碼器問題**: 某些音頻格式可能不穩定

## 修復方案

### 1. 增加重連機制
```python
def _audio_loop(self):
    retry_count = 0
    max_retries = 3
    
    while self.is_playing and retry_count < max_retries:
        try:
            with av.open(self.audio_url, timeout=10) as container:
                # 音頻處理邏輯
                retry_count = 0  # 成功連接後重置重試次數
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                time.sleep(2)  # 等待 2 秒後重試
```

### 2. 延長超時時間
- 從 `timeout=5` 增加到 `timeout=10`
- 給予更多時間建立穩定連線

### 3. 改進錯誤處理
- 分別處理視頻和音頻錯誤
- 設定最大錯誤次數限制
- 提供詳細的錯誤日誌

### 4. 資源管理優化
- 確保所有資源正確關閉
- 避免資源洩漏

## 建議的額外改進

### 1. 音頻流健康檢查
```python
def check_audio_stream_health(self):
    """檢查音頻流是否可用"""
    try:
        with av.open(self.audio_url, timeout=3) as container:
            return len(container.streams.audio) > 0
    except:
        return False
```

### 2. 分離音頻播放和錄影
考慮將播放和錄影的音頻流分開處理，避免衝突。

### 3. 網路狀態監控
```python
def ping_camera(self, ip_address):
    """檢查攝影機網路連線狀態"""
    import subprocess
    try:
        result = subprocess.run(['ping', '-n', '1', ip_address], 
                              capture_output=True, timeout=3)
        return result.returncode == 0
    except:
        return False
```

### 4. 音頻緩衝優化
```python
# 增加音頻緩衝區大小
self.frame_queue = queue.Queue(maxsize=20)  # 從 10 增加到 20
```

## 使用建議

### 1. 網路環境檢查
- 確保攝影機網路連線穩定
- 檢查網路頻寬是否足夠
- 避免網路擁塞時段進行錄影

### 2. 攝影機設定
- 檢查攝影機的音頻設定
- 確認音頻編解碼器支援
- 調整音頻品質設定（如果可能）

### 3. 系統資源
- 監控 CPU 和記憶體使用率
- 確保有足夠的磁碟空間
- 避免同時運行其他高負載程式

### 4. 錄影策略
- 考慮分時錄影（避免兩台攝影機同時錄影）
- 使用較短的錄影片段
- 定期檢查錄影檔案完整性

## 監控和診斷

### 1. 日誌分析
觀察以下日誌訊息：
- 連線嘗試次數
- 錯誤發生頻率
- 網路超時情況

### 2. 效能監控
- 網路延遲測試
- 攝影機回應時間
- 系統資源使用率

### 3. 故障排除步驟
1. 檢查網路連線
2. 重啟攝影機
3. 檢查防火牆設定
4. 測試單一攝影機錄影
5. 檢查音頻 URL 是否正確

## 預期效果

實施這些修復後，應該能夠：
- 減少音頻流中斷頻率
- 提高錄影穩定性
- 提供更好的錯誤診斷資訊
- 自動恢復短暫的網路中斷

如果問題持續存在，建議檢查攝影機韌體版本和網路基礎設施。
